{"openapi": "3.0.0", "paths": {"/equipment/lines": {"get": {"operationId": "getAllEquipmentForAccount", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EquipmentResponse"}}}}, "204": {"description": "No Lines configured for user"}}, "tags": ["equipment"]}}}, "info": {"title": "shift-management-api-equipment", "description": "Shift API Equipment", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"schemas": {"EquipmentProperty": {"type": "object", "properties": {"propertyName": {"type": "string"}, "propertyValue": {"type": "object"}}}, "Equipment": {"type": "object", "properties": {"plantId": {"type": "string"}, "timezone": {"type": "string"}, "equipmentId": {"type": "string"}, "account": {"type": "string"}, "version": {"type": "number"}, "description": {"type": "string"}, "techDesc": {"type": "string"}, "level": {"type": "string"}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/EquipmentProperty"}}, "iconUrl": {"type": "string"}, "createdAt": {"type": "object"}, "updatedAt": {"type": "object"}, "deletedAt": {"type": "object"}, "expireAt": {"type": "number"}}, "required": ["plantId", "timezone", "equipmentId", "account", "version", "description", "techDesc", "level", "properties"]}, "EquipmentResponse": {"type": "object", "properties": {"equipment": {"type": "array", "items": {"$ref": "#/components/schemas/Equipment"}}}, "required": ["equipment"]}}}}