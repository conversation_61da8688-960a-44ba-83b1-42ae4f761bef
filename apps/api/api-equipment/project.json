{"name": "app-api-equipment", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/api-equipment/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "progress": false, "generatePackageJson": true, "isolatedConfig": true, "optimization": true, "outputPath": "dist/apps/api/api-equipment", "main": "apps/api/api-equipment/src/lambda.ts", "tsConfig": "apps/api/api-equipment/tsconfig.app.json", "assets": [], "webpackConfig": "webpack.config.js", "sourceMap": true}, "configurations": {"swagger": {"main": "apps/api/api-equipment/src/swagger.ts", "isolatedConfig": false, "webpackConfig": "", "tsPlugins": [{"name": "@nestjs/swagger/plugin", "options": {"dtoFileNameSuffix": [".entity.ts", ".dto.ts"], "controllerFileNameSuffix": [".controller.ts"], "classValidatorShim": true, "dtoKeyOfComment": "description", "controllerKeyOfComment": "description", "introspectComments": true}}]}, "production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/api/api-equipment/src/environments/environment.ts", "with": "apps/api/api-equipment/src/environments/environment.prod.ts"}]}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/reports/coverage/{projectRoot}"], "options": {"jestConfig": "apps/api/api-equipment/jest.config.ts"}}, "bundle": {"executor": "nx:run-commands", "dependsOn": ["build"], "options": {"cwd": "dist/apps/api/api-equipment/nodejs", "parallel": false, "commands": ["echo '>>> Installing dependencies..'", "pnpm i --config.node-linker=hoisted --prod --ignore-workspace --registry=https://syskronx.jfrog.io/syskronx/api/npm/npm/"]}}, "serve": {"executor": "@nx/js:node", "options": {"buildTarget": "app-api-equipment:build:swagger"}}, "gen-swagger": {"dependsOn": ["build"], "executor": "@nx/js:node", "options": {"buildTarget": "app-api-equipment:build:swagger", "args": ["--export", "--export-path=apps/api/api-equipment/docs"], "inspect": false, "watch": false}}, "gen-client": {"dependsOn": ["gen-swagger"], "executor": "nx:run-commands", "options": {"parallel": false, "commands": ["pnpm npx ng-openapi-gen -c apps/api/api-equipment/ng-openapi-gen.json", "pnpm run lint:fix:format"]}}, "lint-client": {"dependsOn": ["gen-client"], "executor": "./tools/executors/workspace:lint-openapi-gen", "options": {"config": "apps/api/api-equipment/ng-openapi-gen.json"}}}, "tags": ["type:app", "scope:api", "platform:backend"]}