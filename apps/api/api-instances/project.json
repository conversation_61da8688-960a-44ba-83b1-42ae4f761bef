{"name": "app-api-instances", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/api-instances/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "progress": true, "generatePackageJson": true, "isolatedConfig": true, "optimization": true, "outputPath": "dist/apps/api/api-instances", "main": "apps/api/api-instances/src/lambda.ts", "tsConfig": "apps/api/api-instances/tsconfig.app.json", "webpackConfig": "webpack.config.js", "sourceMap": true}, "configurations": {"swagger": {"main": "apps/api/api-instances/src/swagger.ts", "isolatedConfig": false, "webpackConfig": "", "tsPlugins": [{"name": "@nestjs/swagger/plugin", "options": {"dtoFileNameSuffix": [".entity.ts", ".dto.ts"], "controllerFileNameSuffix": [".controller.ts"], "classValidatorShim": true, "dtoKeyOfComment": "description", "controllerKeyOfComment": "description", "introspectComments": true}}]}, "production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/api/api-instances/src/environments/environment.ts", "with": "apps/api/api-instances/src/environments/environment.prod.ts"}]}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/reports/coverage/{projectRoot}"], "options": {"jestConfig": "apps/api/api-instances/jest.config.ts"}}, "bundle": {"executor": "nx:run-commands", "dependsOn": ["build"], "options": {"cwd": "dist/apps/api/api-instances/nodejs", "parallel": false, "commands": ["echo '>>> Installing dependencies..'", "pnpm i --config.node-linker=hoisted --prod --ignore-workspace --registry=https://syskronx.jfrog.io/syskronx/api/npm/npm/"]}}, "serve": {"executor": "@nx/js:node", "options": {"buildTarget": "app-api-instances:build:swagger"}}, "gen-swagger": {"dependsOn": ["build"], "executor": "@nx/js:node", "options": {"buildTarget": "app-api-instances:build:swagger", "args": ["--export", "--export-path=apps/api/api-instances/docs"], "inspect": false, "watch": false}}, "gen-client": {"dependsOn": ["gen-swagger"], "executor": "nx:run-commands", "options": {"parallel": false, "commands": ["pnpm npx ng-openapi-gen -c apps/api/api-instances/ng-openapi-gen.json", "pnpm run lint:fix:format"]}}, "lint-client": {"dependsOn": ["gen-client"], "executor": "./tools/executors/workspace:lint-openapi-gen", "options": {"config": "apps/api/api-instances/ng-openapi-gen.json"}}}, "tags": ["type:app", "scope:api", "platform:backend"]}