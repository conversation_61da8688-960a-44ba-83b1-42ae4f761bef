import { DocumentBuilder } from '@nestjs/swagger';

import { runSwagger } from '@shift-management/api/util/nestjs/api-util-swagger';

import { AppModule } from './app/app.module';
import applicationOptions from './configuration';

const config = new DocumentBuilder()
  .setTitle('shift-management-api-instances')
  .setDescription('Shift API Instances')
  .setVersion('1.0');

void runSwagger(AppModule, config, applicationOptions);
